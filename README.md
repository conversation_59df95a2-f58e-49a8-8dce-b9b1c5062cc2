# Figure Diagram Processor

This Python script processes images containing product figure diagrams with annotated 'Fig xx' labels. It automatically detects the coordinates of 'Fig xx' boxes and uses them to identify, split, and extract figure diagrams from the images.

## Features

- **Automatic OCR Detection**: Uses Tesseract OCR to detect 'Fig xx' annotations in images
- **Smart Image Processing**: Handles various orientations and layouts of figure diagrams
- **Intelligent Splitting**: Automatically splits images with multiple figures based on detected annotations
- **Validation**: Checks for black patterns and white lines to ensure proper processing
- **Organized Output**: Categorizes processed images into different folders based on processing outcomes

## Algorithm Overview

The script implements a sophisticated algorithm that:

1. **Detects 'Fig xx' boxes** using OCR analysis
2. **Processes based on number of detected boxes**:
   - **Single Fig**: Validates orientation, rotates if needed, crops out annotation
   - **Dual Figs**: Determines alignment, splits image appropriately
   - **Multiple Figs**: Groups by alignment, applies recursive processing with proportional splitting

## Installation

1. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Install Tesseract OCR**:
   - **Windows**: Download from [GitHub releases](https://github.com/UB-Mannheim/tesseract/wiki)
   - **macOS**: `brew install tesseract`
   - **Ubuntu/Debian**: `sudo apt-get install tesseract-ocr`

## Usage

### Command Line Interface

```bash
# Process a single image
python figure_diagram_processor.py input_image.png

# Process all images in a directory
python figure_diagram_processor.py /path/to/images/

# Specify custom output directory
python figure_diagram_processor.py input_image.png -o /path/to/output/

# Enable verbose logging
python figure_diagram_processor.py input_image.png -v
```

### Python API

```python
from figure_diagram_processor import FigureProcessor

# Initialize processor
processor = FigureProcessor(output_dir="my_output")

# Process a single image
success = processor.process_image("path/to/image.png")
```

## Output Structure

The script creates an organized output directory structure:

```
output/
├── processed/          # Successfully processed images
├── 1/                 # Single fig validation failed
├── 2a/                # Two figs horizontal split failed
├── 2b/                # Two figs not aligned
├── 2g/                # Multiple figs group split failed
├── 1g/                # Single group proportional split failed
├── 3g/                # Three groups proportional split failed
└── HV/                # Mixed horizontal/vertical orientations
```

## Processing Logic

### Single 'Fig xx' Detection
1. Check if vertically positioned → rotate if needed
2. Validate no black patterns around the annotation
3. Find white lines for clean cropping
4. Extract figure diagram without annotation

### Dual 'Fig xx' Detection
1. Rotate if any boxes are vertical
2. Determine alignment (vertical or horizontal)
3. Split image at appropriate white lines
4. Process each part separately

### Multiple 'Fig xx' Detection
1. Handle mixed orientations
2. Group boxes by alignment
3. Apply recursive processing for complex layouts
4. Use proportional splitting for evenly distributed figures

## Configuration

Key parameters can be adjusted in the `FigureProcessor` class:

- `alignment_margin_percent`: Margin of error for alignment detection (default: 0.5%)
- `white_line_threshold`: Threshold for white pixel detection (default: 250)
- `black_pattern_threshold`: Threshold for black pattern detection (default: 50)

## Supported Image Formats

- PNG
- JPEG/JPG
- TIFF
- BMP

## Error Handling

The script includes comprehensive error handling and logging:
- OCR failures are logged and handled gracefully
- Images that cannot be processed are categorized into appropriate folders
- Detailed logging helps diagnose processing issues

## Requirements

- Python 3.7+
- OpenCV (cv2)
- Tesseract OCR
- NumPy
- Pillow

## Troubleshooting

1. **OCR not working**: Ensure Tesseract is properly installed and in PATH
2. **Poor detection**: Try preprocessing images (contrast adjustment, noise reduction)
3. **Memory issues**: Process large images in batches
4. **Path issues**: Use absolute paths for input/output directories

## Example Usage

```bash
# Process patent diagrams
python figure_diagram_processor.py patent_diagrams/ -o processed_patents/ -v

# Process single technical drawing
python figure_diagram_processor.py technical_drawing.png -o output/
```

This will automatically detect 'Fig xx' annotations and extract clean figure diagrams for further analysis or documentation.
