#!/usr/bin/env python3
"""
Figure Diagram Processor

This script processes images containing product figure diagrams with annotated 'Fig xx' labels.
It detects the coordinates of 'Fig xx' boxes and uses them to identify and split figure diagrams.

Author: AI Assistant
Date: 2025-08-08
"""

import os
import cv2
import numpy as np
import pytesseract
import re
import logging
import argparse
import platform

from pathlib import Path
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass
import shutil

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
# Configure pytesseract executable path (Windows-friendly)
try:
    tesseract_env = os.environ.get("TESSERACT_CMD")
    if tesseract_env and os.path.exists(tesseract_env):
        pytesseract.pytesseract.tesseract_cmd = tesseract_env
        logger.info(f"Using Tesseract from TESSERACT_CMD: {tesseract_env}")
    elif platform.system() == "Windows":
        possible_paths = [
            r"C:\\Program Files\\Tesseract-OCR\\tesseract.exe",
            r"C:\\Program Files (x86)\\Tesseract-OCR\\tesseract.exe",
            r"C:\\ProgramData\\chocolatey\\bin\\tesseract.exe",
        ]
        for p in possible_paths:
            if os.path.exists(p):
                pytesseract.pytesseract.tesseract_cmd = p
                logger.info(f"Using Tesseract found at: {p}")
                break
except Exception:
    # Non-fatal; detect_fig_boxes will log a clear error if tesseract is unavailable
    pass


@dataclass
class BoundingBox:
    """Represents a bounding box with coordinates and text content."""
    x: int
    y: int
    width: int
    height: int
    text: str
    confidence: float = 0.0

    @property
    def center_x(self) -> int:
        return self.x + self.width // 2

    @property
    def center_y(self) -> int:
        return self.y + self.height // 2

    @property
    def right(self) -> int:
        return self.x + self.width

    @property
    def bottom(self) -> int:
        return self.y + self.height

class FigureProcessor:
    """Main class for processing figure diagrams with 'Fig xx' annotations."""

    def __init__(self, output_dir: str = "output"):
        """
        Initialize the processor.

        Args:
            output_dir: Directory to store processed images and results
        """
        self.output_dir = Path(output_dir)
        self.setup_output_directories()

        # Configuration parameters
        self.alignment_margin_percent = 0.5  # Margin of error for alignment detection
        self.white_line_threshold = 250  # Threshold for white pixel detection
        self.black_pattern_threshold = 50  # Threshold for black pattern detection

    def setup_output_directories(self):
        """Create necessary output directories."""
        directories = [
            self.output_dir,
            self.output_dir / "processed",
            self.output_dir / "1",      # Single fig validation failed
            self.output_dir / "2a",     # Two figs horizontal split failed
            self.output_dir / "2b",     # Two figs not aligned
            self.output_dir / "2g",     # Multiple figs group split failed
            self.output_dir / "1g",     # Single group proportional split failed
            self.output_dir / "3g",     # Three groups proportional split failed
            self.output_dir / "HV",     # Mixed horizontal/vertical orientations
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    def detect_fig_boxes(self, image: np.ndarray) -> List[BoundingBox]:
        """
        Detect 'Fig xx' annotated boxes in the image using OCR.

        Args:
            image: Input image as numpy array

        Returns:
            List of BoundingBox objects containing 'Fig xx' text
        """
        logger.info("Detecting 'Fig xx' boxes using OCR...")

        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Use pytesseract to get detailed OCR data
        try:
            data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)
        except Exception as e:
            logger.error(f"OCR failed: {e}")
            return []

        fig_boxes = []
        fig_pattern = re.compile(r'Fig\s*\d+', re.IGNORECASE)

        # Process OCR results
        for i in range(len(data['text'])):
            text = data['text'][i].strip()
            confidence = int(data['conf'][i])

            # Skip low confidence or empty text
            if confidence < 30 or not text:
                continue

            # Check if text matches 'Fig xx' pattern
            if fig_pattern.search(text):
                x = data['left'][i]
                y = data['top'][i]
                w = data['width'][i]
                h = data['height'][i]

                if w > 0 and h > 0:  # Valid bounding box
                    bbox = BoundingBox(x, y, w, h, text, confidence)
                    fig_boxes.append(bbox)
                    logger.info(f"Found 'Fig xx' box: {text} at ({x}, {y}, {w}, {h})")

        return fig_boxes

    def is_vertically_positioned(self, bbox: BoundingBox) -> bool:
        """
        Determine if a bounding box is vertically positioned (height > width).

        Args:
            bbox: BoundingBox to check

        Returns:
            True if vertically positioned, False otherwise
        """
        return bbox.height > bbox.width

    def has_black_pattern_around(self, image: np.ndarray, bbox: BoundingBox,
                                direction: str) -> bool:
        """
        Check for black patterns around a bounding box in specified direction.

        Args:
            image: Input image
            bbox: BoundingBox to check around
            direction: 'horizontal' or 'vertical'

        Returns:
            True if black pattern detected, False otherwise
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        h, w = gray.shape

        if direction == 'horizontal':
            # Check left and right of bbox
            left_region = gray[bbox.y:bbox.bottom, max(0, bbox.x-20):bbox.x]
            right_region = gray[bbox.y:bbox.bottom, bbox.right:min(w, bbox.right+20)]

            regions = [left_region, right_region]
        else:  # vertical
            # Check above and below bbox
            above_region = gray[max(0, bbox.y-20):bbox.y, bbox.x:bbox.right]
            below_region = gray[bbox.bottom:min(h, bbox.bottom+20), bbox.x:bbox.right]

            regions = [above_region, below_region]

        # Check if any region has significant black patterns
        for region in regions:
            if region.size > 0:
                black_pixels = np.sum(region < self.black_pattern_threshold)
                total_pixels = region.size
                if black_pixels / total_pixels > 0.3:  # 30% black pixels threshold
                    return True

        return False

    def rotate_image_90(self, image: np.ndarray) -> np.ndarray:
        """
        Rotate image 90 degrees clockwise.

        Args:
            image: Input image

        Returns:
            Rotated image
        """
        return cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE)

    def find_white_line(self, image: np.ndarray, bbox: BoundingBox,
                       direction: str, position: str) -> Optional[int]:
        """
        Find a white line near the bounding box for cropping.

        Args:
            image: Input image
            bbox: BoundingBox reference
            direction: 'horizontal' or 'vertical'
            position: 'above', 'below', 'left', 'right'

        Returns:
            Line coordinate if found, None otherwise
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        h, w = gray.shape

        # Search for white line within 5 pixels
        for offset in range(1, 6):
            if direction == 'horizontal':
                if position == 'above':
                    line_y = max(0, bbox.y - offset)
                    line = gray[line_y, bbox.x:bbox.right]
                elif position == 'below':
                    line_y = min(h-1, bbox.bottom + offset)
                    line = gray[line_y, bbox.x:bbox.right]
                else:
                    continue

                # Check if line is white (all pixels above threshold)
                if np.all(line >= self.white_line_threshold):
                    return line_y

            else:  # vertical
                if position == 'left':
                    line_x = max(0, bbox.x - offset)
                    line = gray[bbox.y:bbox.bottom, line_x]
                elif position == 'right':
                    line_x = min(w-1, bbox.right + offset)
                    line = gray[bbox.y:bbox.bottom, line_x]
                else:
                    continue

                # Check if line is white (all pixels above threshold)
                if np.all(line >= self.white_line_threshold):
                    return line_x

        return None

    def are_vertically_aligned(self, bbox1: BoundingBox, bbox2: BoundingBox,
                              image_width: int) -> bool:
        """
        Check if two bounding boxes are vertically aligned.

        Args:
            bbox1, bbox2: BoundingBoxes to compare
            image_width: Width of the image for margin calculation

        Returns:
            True if vertically aligned within margin of error
        """
        margin = int(image_width * self.alignment_margin_percent / 100)
        return abs(bbox1.center_x - bbox2.center_x) <= margin

    def are_horizontally_aligned(self, bbox1: BoundingBox, bbox2: BoundingBox,
                                image_height: int) -> bool:
        """
        Check if two bounding boxes are horizontally aligned.

        Args:
            bbox1, bbox2: BoundingBoxes to compare
            image_height: Height of the image for margin calculation

        Returns:
            True if horizontally aligned within margin of error
        """
        margin = int(image_height * self.alignment_margin_percent / 100)
        return abs(bbox1.center_y - bbox2.center_y) <= margin

    def crop_fig_box(self, image: np.ndarray, bbox: BoundingBox,
                     white_line_coord: Optional[int] = None,
                     direction: str = 'horizontal') -> np.ndarray:
        """
        Crop out the 'Fig xx' box from the image.

        Args:
            image: Input image
            bbox: BoundingBox to crop out
            white_line_coord: Coordinate of white line for cropping boundary
            direction: Direction of the white line

        Returns:
            Image with 'Fig xx' box cropped out
        """
        result = image.copy()

        if white_line_coord is not None:
            if direction == 'horizontal':
                # Crop horizontally at the white line
                if white_line_coord < bbox.y:  # White line above bbox
                    result = result[white_line_coord:, :]
                else:  # White line below bbox
                    result = result[:white_line_coord, :]
            else:  # vertical
                # Crop vertically at the white line
                if white_line_coord < bbox.x:  # White line left of bbox
                    result = result[:, white_line_coord:]
                else:  # White line right of bbox
                    result = result[:, :white_line_coord]
        else:
            # Simple crop - remove the bbox area by filling with white
            cv2.rectangle(result, (bbox.x, bbox.y), (bbox.right, bbox.bottom),
                         (255, 255, 255), -1)

        return result

    def process_single_fig(self, image: np.ndarray, bbox: BoundingBox,
                          filename: str) -> bool:
        """
        Process image with single 'Fig xx' annotation.

        Args:
            image: Input image
            bbox: Single BoundingBox found
            filename: Original filename for reference

        Returns:
            True if processing successful, False otherwise
        """
        logger.info(f"Processing single 'Fig xx' in {filename}")

        # Check if vertically positioned
        if self.is_vertically_positioned(bbox):
            logger.info("Fig box is vertically positioned")

            # Validate: no black pattern to left or right
            if self.has_black_pattern_around(image, bbox, 'horizontal'):
                logger.warning("Black pattern detected around vertical Fig box")
                self.copy_to_folder(image, filename, "1")
                return False

            # Rotate image
            image = self.rotate_image_90(image)
            # Note: After rotation, bbox coordinates would need to be recalculated
            # For simplicity, we'll re-detect the bbox after rotation
            rotated_boxes = self.detect_fig_boxes(image)
            if not rotated_boxes:
                logger.error("Could not detect Fig box after rotation")
                self.copy_to_folder(image, filename, "1")
                return False
            bbox = rotated_boxes[0]  # Take the first one

        # Now process horizontally positioned Fig box
        logger.info("Processing horizontally positioned Fig box")

        # Validate: no black pattern above or below
        if self.has_black_pattern_around(image, bbox, 'vertical'):
            logger.warning("Black pattern detected around horizontal Fig box")
            self.copy_to_folder(image, filename, "1")
            return False

        # Find white line for cropping
        white_line_above = self.find_white_line(image, bbox, 'horizontal', 'above')
        white_line_below = self.find_white_line(image, bbox, 'horizontal', 'below')

        # Crop the Fig box out
        if white_line_above is not None:
            processed_image = self.crop_fig_box(image, bbox, white_line_above, 'horizontal')
        elif white_line_below is not None:
            processed_image = self.crop_fig_box(image, bbox, white_line_below, 'horizontal')
        else:
            processed_image = self.crop_fig_box(image, bbox)

        # Save processed image
        output_path = self.output_dir / "processed" / filename
        cv2.imwrite(str(output_path), processed_image)
        logger.info(f"Successfully processed single Fig image: {output_path}")

        return True

    def split_image_horizontally(self, image: np.ndarray, split_y: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        Split image horizontally at given y coordinate.

        Args:
            image: Input image
            split_y: Y coordinate to split at

        Returns:
            Tuple of (top_part, bottom_part)
        """
        top_part = image[:split_y, :]
        bottom_part = image[split_y:, :]
        return top_part, bottom_part

    def split_image_vertically(self, image: np.ndarray, split_x: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        Split image vertically at given x coordinate.

        Args:
            image: Input image
            split_x: X coordinate to split at

        Returns:
            Tuple of (left_part, right_part)
        """
        left_part = image[:, :split_x]
        right_part = image[:, split_x:]
        return left_part, right_part

    def find_white_split_line(self, image: np.ndarray, direction: str,
                             start_pos: int, search_range: int = 10) -> Optional[int]:
        """
        Find a white line suitable for splitting the image.

        Args:
            image: Input image
            direction: 'horizontal' or 'vertical'
            start_pos: Starting position to search from
            search_range: Range of pixels to search

        Returns:
            Coordinate of white line if found, None otherwise
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        h, w = gray.shape

        for offset in range(-search_range, search_range + 1):
            if direction == 'horizontal':
                y = start_pos + offset
                if 0 <= y < h:
                    line = gray[y, :]
                    if np.all(line >= self.white_line_threshold):
                        return y
            else:  # vertical
                x = start_pos + offset
                if 0 <= x < w:
                    line = gray[:, x]
                    if np.all(line >= self.white_line_threshold):
                        return x

        return None

    def process_dual_fig(self, image: np.ndarray, boxes: List[BoundingBox],
                        filename: str) -> bool:
        """
        Process image with two 'Fig xx' annotations.

        Args:
            image: Input image
            boxes: List of two BoundingBoxes
            filename: Original filename for reference

        Returns:
            True if processing successful, False otherwise
        """
        logger.info(f"Processing dual 'Fig xx' in {filename}")

        bbox1, bbox2 = boxes[0], boxes[1]
        h, w = image.shape[:2]

        # Check if any boxes are vertically positioned and rotate if needed
        if any(self.is_vertically_positioned(bbox) for bbox in boxes):
            logger.info("Rotating image due to vertical Fig boxes")
            image = self.rotate_image_90(image)
            # Re-detect boxes after rotation
            rotated_boxes = self.detect_fig_boxes(image)
            if len(rotated_boxes) != 2:
                logger.error("Could not detect 2 Fig boxes after rotation")
                self.copy_to_folder(image, filename, "2b")
                return False
            bbox1, bbox2 = rotated_boxes[0], rotated_boxes[1]
            h, w = image.shape[:2]  # Update dimensions

        # Check alignment
        if self.are_vertically_aligned(bbox1, bbox2, w):
            logger.info("Fig boxes are vertically aligned")
            return self.process_vertically_aligned_figs(image, bbox1, bbox2, filename)
        elif self.are_horizontally_aligned(bbox1, bbox2, h):
            logger.info("Fig boxes are horizontally aligned")
            return self.process_horizontally_aligned_figs(image, bbox1, bbox2, filename)
        else:
            logger.warning("Fig boxes are not aligned")
            self.copy_to_folder(image, filename, "2b")
            return False

    def process_vertically_aligned_figs(self, image: np.ndarray, bbox1: BoundingBox,
                                       bbox2: BoundingBox, filename: str) -> bool:
        """
        Process two vertically aligned Fig boxes.

        Args:
            image: Input image
            bbox1, bbox2: Two vertically aligned BoundingBoxes
            filename: Original filename

        Returns:
            True if processing successful, False otherwise
        """
        h, w = image.shape[:2]

        # Sort boxes by y coordinate (top to bottom)
        if bbox1.y > bbox2.y:
            bbox1, bbox2 = bbox2, bbox1

        # Determine if Fig boxes are above or below figure diagrams
        # Check if first box has nothing above (indicating it's above the diagram)
        top_region = image[:bbox1.y, :]
        if len(top_region) > 0 and np.mean(top_region) > 240:  # Mostly white above
            logger.info("Fig boxes are above the figure diagram")
            # Draw line above the second Fig box
            split_line = self.find_white_split_line(image, 'horizontal', bbox2.y - 5)
            if split_line is None:
                logger.error("Could not find white line for splitting")
                self.copy_to_folder(image, filename, "2b")
                return False
        else:
            logger.info("Fig boxes are below the figure diagram")
            # Draw line below the first Fig box
            split_line = self.find_white_split_line(image, 'horizontal', bbox1.bottom + 5)
            if split_line is None:
                logger.error("Could not find white line for splitting")
                self.copy_to_folder(image, filename, "2b")
                return False

        # Split the image
        top_part, bottom_part = self.split_image_horizontally(image, split_line)

        # Crop out Fig boxes from each part
        # This is a simplified approach - in practice, you'd need to adjust coordinates
        processed_top = self.crop_fig_box(top_part, bbox1)
        processed_bottom = self.crop_fig_box(bottom_part, bbox2)

        # Save both parts
        base_name = Path(filename).stem
        ext = Path(filename).suffix

        top_path = self.output_dir / "processed" / f"{base_name}_top{ext}"
        bottom_path = self.output_dir / "processed" / f"{base_name}_bottom{ext}"

        cv2.imwrite(str(top_path), processed_top)
        cv2.imwrite(str(bottom_path), processed_bottom)

        logger.info(f"Successfully split vertically aligned Figs: {top_path}, {bottom_path}")
        return True

    def process_horizontally_aligned_figs(self, image: np.ndarray, bbox1: BoundingBox,
                                         bbox2: BoundingBox, filename: str) -> bool:
        """
        Process two horizontally aligned Fig boxes.

        Args:
            image: Input image
            bbox1, bbox2: Two horizontally aligned BoundingBoxes
            filename: Original filename

        Returns:
            True if processing successful, False otherwise
        """
        h, w = image.shape[:2]

        # Try to split from the middle of the image
        middle_x = w // 2
        split_line = self.find_white_split_line(image, 'vertical', middle_x)

        if split_line is None:
            logger.error("Could not find white vertical line for splitting")
            self.copy_to_folder(image, filename, "2a")
            return False

        # Split the image vertically
        left_part, right_part = self.split_image_vertically(image, split_line)

        # Determine which box belongs to which part and crop
        if bbox1.center_x < split_line:
            processed_left = self.crop_fig_box(left_part, bbox1)
            processed_right = self.crop_fig_box(right_part, bbox2)
        else:
            processed_left = self.crop_fig_box(left_part, bbox2)
            processed_right = self.crop_fig_box(right_part, bbox1)

        # Save both parts
        base_name = Path(filename).stem
        ext = Path(filename).suffix

        left_path = self.output_dir / "processed" / f"{base_name}_left{ext}"
        right_path = self.output_dir / "processed" / f"{base_name}_right{ext}"

        cv2.imwrite(str(left_path), processed_left)
        cv2.imwrite(str(right_path), processed_right)

        logger.info(f"Successfully split horizontally aligned Figs: {left_path}, {right_path}")
        return True

    def copy_to_folder(self, image: np.ndarray, filename: str, folder_name: str):
        """
        Copy image to specified output folder.

        Args:
            image: Image to save
            filename: Original filename
            folder_name: Target folder name
        """
        output_path = self.output_dir / folder_name / filename
        cv2.imwrite(str(output_path), image)
        logger.info(f"Copied image to {output_path}")

    def group_boxes_by_y(self, boxes: List[BoundingBox], image_height: int) -> List[List[BoundingBox]]:
        """
        Group bounding boxes by Y coordinate (horizontally aligned groups).

        Args:
            boxes: List of BoundingBoxes to group
            image_height: Height of image for margin calculation

        Returns:
            List of groups, each containing horizontally aligned boxes
        """
        if not boxes:
            return []

        # Sort boxes by y coordinate
        sorted_boxes = sorted(boxes, key=lambda b: b.center_y)
        groups = []
        current_group = [sorted_boxes[0]]

        for box in sorted_boxes[1:]:
            # Check if this box is horizontally aligned with the current group
            if self.are_horizontally_aligned(current_group[0], box, image_height):
                current_group.append(box)
            else:
                # Start a new group
                groups.append(current_group)
                current_group = [box]

        # Add the last group
        groups.append(current_group)
        return groups

    def process_multiple_figs(self, image: np.ndarray, boxes: List[BoundingBox],
                             filename: str) -> bool:
        """
        Process image with multiple (>2) 'Fig xx' annotations.

        Args:
            image: Input image
            boxes: List of BoundingBoxes (>2)
            filename: Original filename

        Returns:
            True if processing successful, False otherwise
        """
        logger.info(f"Processing {len(boxes)} 'Fig xx' boxes in {filename}")

        # Check orientations
        vertical_boxes = [box for box in boxes if self.is_vertically_positioned(box)]
        horizontal_boxes = [box for box in boxes if not self.is_vertically_positioned(box)]

        # If all vertical, rotate
        if len(vertical_boxes) == len(boxes):
            logger.info("All Fig boxes are vertical, rotating image")
            image = self.rotate_image_90(image)
            # Re-detect boxes after rotation
            rotated_boxes = self.detect_fig_boxes(image)
            if len(rotated_boxes) != len(boxes):
                logger.error(f"Expected {len(boxes)} boxes after rotation, got {len(rotated_boxes)}")
                self.copy_to_folder(image, filename, "HV")
                return False
            boxes = rotated_boxes
        # If mixed orientations, copy to HV folder
        elif len(vertical_boxes) > 0 and len(horizontal_boxes) > 0:
            logger.warning("Mixed horizontal and vertical orientations detected")
            self.copy_to_folder(image, filename, "HV")
            return False

        # Group boxes by Y coordinate
        h, w = image.shape[:2]
        groups = self.group_boxes_by_y(boxes, h)

        logger.info(f"Found {len(groups)} horizontal groups")

        if len(groups) == 1:
            # Single group - try proportional split
            return self.process_single_group_proportional(image, groups[0], filename)
        elif len(groups) == 2:
            # Two groups - split in the middle
            return self.process_two_groups(image, groups, filename)
        elif len(groups) == 3:
            # Three groups - proportional split (1/3, 1/3, 1/3)
            return self.process_three_groups(image, groups, filename)
        else:
            logger.warning(f"Unsupported number of groups: {len(groups)}")
            self.copy_to_folder(image, filename, "HV")
            return False

    def process_single_group_proportional(self, image: np.ndarray,
                                         group: List[BoundingBox], filename: str) -> bool:
        """
        Process single group with proportional splitting.

        Args:
            image: Input image
            group: List of BoundingBoxes in the group
            filename: Original filename

        Returns:
            True if processing successful, False otherwise
        """
        logger.info(f"Processing single group with {len(group)} Fig boxes proportionally")

        h, w = image.shape[:2]
        num_figs = len(group)

        # Sort boxes by x coordinate (left to right)
        sorted_boxes = sorted(group, key=lambda b: b.center_x)

        # Calculate split positions
        split_width = w // num_figs
        splits = []

        for i in range(1, num_figs):
            split_x = i * split_width
            # Try to find a white line near this position
            white_line = self.find_white_split_line(image, 'vertical', split_x)
            if white_line is None:
                logger.error(f"Could not find white line for proportional split at {split_x}")
                self.copy_to_folder(image, filename, "1g")
                return False
            splits.append(white_line)

        # Split the image into parts
        parts = []
        prev_x = 0

        for split_x in splits:
            part = image[:, prev_x:split_x]
            parts.append(part)
            prev_x = split_x

        # Add the last part
        parts.append(image[:, prev_x:])

        # Save each part
        base_name = Path(filename).stem
        ext = Path(filename).suffix

        for i, part in enumerate(parts):
            part_path = self.output_dir / "processed" / f"{base_name}_part{i+1}{ext}"
            cv2.imwrite(str(part_path), part)

        logger.info(f"Successfully split single group into {len(parts)} parts")
        return True

    def process_two_groups(self, image: np.ndarray, groups: List[List[BoundingBox]],
                          filename: str) -> bool:
        """
        Process two groups by splitting in the middle.

        Args:
            image: Input image
            groups: List of two groups
            filename: Original filename

        Returns:
            True if processing successful, False otherwise
        """
        logger.info("Processing two groups by splitting in the middle")

        h, w = image.shape[:2]

        # Find the middle point between the two groups
        group1_y = np.mean([box.center_y for box in groups[0]])
        group2_y = np.mean([box.center_y for box in groups[1]])
        middle_y = int((group1_y + group2_y) / 2)

        # Find white line for splitting
        split_line = self.find_white_split_line(image, 'horizontal', middle_y)
        if split_line is None:
            logger.error("Could not find white line for two-group split")
            self.copy_to_folder(image, filename, "2g")
            return False

        # Split the image
        top_part, bottom_part = self.split_image_horizontally(image, split_line)

        # Process each group recursively if it has more than 1 box
        base_name = Path(filename).stem
        ext = Path(filename).suffix

        # Process top group
        if len(groups[0]) > 1:
            top_filename = f"{base_name}_top{ext}"
            self.process_multiple_figs(top_part, groups[0], top_filename)
        else:
            top_path = self.output_dir / "processed" / f"{base_name}_top{ext}"
            processed_top = self.crop_fig_box(top_part, groups[0][0])
            cv2.imwrite(str(top_path), processed_top)

        # Process bottom group
        if len(groups[1]) > 1:
            bottom_filename = f"{base_name}_bottom{ext}"
            self.process_multiple_figs(bottom_part, groups[1], bottom_filename)
        else:
            bottom_path = self.output_dir / "processed" / f"{base_name}_bottom{ext}"
            processed_bottom = self.crop_fig_box(bottom_part, groups[1][0])
            cv2.imwrite(str(bottom_path), processed_bottom)

        logger.info("Successfully processed two groups")
        return True

    def process_three_groups(self, image: np.ndarray, groups: List[List[BoundingBox]],
                            filename: str) -> bool:
        """
        Process three groups with proportional splitting (1/3, 1/3, 1/3).

        Args:
            image: Input image
            groups: List of three groups
            filename: Original filename

        Returns:
            True if processing successful, False otherwise
        """
        logger.info("Processing three groups with proportional splitting")

        h, w = image.shape[:2]

        # Calculate split positions (1/3 and 2/3)
        split1_y = h // 3
        split2_y = 2 * h // 3

        # Find white lines for splitting
        split_line1 = self.find_white_split_line(image, 'horizontal', split1_y)
        split_line2 = self.find_white_split_line(image, 'horizontal', split2_y)

        if split_line1 is None or split_line2 is None:
            logger.error("Could not find white lines for three-group split")
            self.copy_to_folder(image, filename, "3g")
            return False

        # Split the image into three parts
        part1 = image[:split_line1, :]
        part2 = image[split_line1:split_line2, :]
        part3 = image[split_line2:, :]

        parts = [part1, part2, part3]
        base_name = Path(filename).stem
        ext = Path(filename).suffix

        # Process each part
        for i, (part, group) in enumerate(zip(parts, groups)):
            if len(group) > 1:
                part_filename = f"{base_name}_part{i+1}{ext}"
                self.process_multiple_figs(part, group, part_filename)
            else:
                part_path = self.output_dir / "processed" / f"{base_name}_part{i+1}{ext}"
                processed_part = self.crop_fig_box(part, group[0])
                cv2.imwrite(str(part_path), processed_part)

        logger.info("Successfully processed three groups")
        return True

    def process_image(self, image_path: str) -> bool:
        """
        Main processing function for a single image.

        Args:
            image_path: Path to the input image

        Returns:
            True if processing successful, False otherwise
        """
        logger.info(f"Processing image: {image_path}")

        # Load image
        image = cv2.imread(image_path)
        if image is None:
            logger.error(f"Could not load image: {image_path}")
            return False

        filename = Path(image_path).name

        # Step 1: Detect 'Fig xx' boxes
        fig_boxes = self.detect_fig_boxes(image)

        if not fig_boxes:
            logger.warning(f"No 'Fig xx' boxes found in {filename}")
            return False

        logger.info(f"Found {len(fig_boxes)} 'Fig xx' boxes")

        # Step 2: Process based on number of boxes found
        if len(fig_boxes) == 1:
            return self.process_single_fig(image, fig_boxes[0], filename)
        elif len(fig_boxes) == 2:
            return self.process_dual_fig(image, fig_boxes, filename)
        else:  # len(fig_boxes) > 2
            return self.process_multiple_figs(image, fig_boxes, filename)


def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="Process images with 'Fig xx' annotations to extract figure diagrams"
    )
    parser.add_argument(
        "input_path",
        help="Path to input image file or directory containing images"
    )
    parser.add_argument(
        "-o", "--output",
        default="output",
        help="Output directory (default: output)"
    )
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize processor
    processor = FigureProcessor(args.output)

    input_path = Path(args.input_path)

    if input_path.is_file():
        # Process single file
        if input_path.suffix.lower() in ['.png', '.jpg', '.jpeg', '.tiff', '.bmp']:
            success = processor.process_image(str(input_path))
            if success:
                logger.info("Processing completed successfully")
            else:
                logger.error("Processing failed")
                return 1
        else:
            logger.error(f"Unsupported file format: {input_path.suffix}")
            return 1

    elif input_path.is_dir():
        # Process all images in directory
        image_extensions = ['.png', '.jpg', '.jpeg', '.tiff', '.bmp']
        image_files = []

        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))

        if not image_files:
            logger.error(f"No image files found in {input_path}")
            return 1

        logger.info(f"Found {len(image_files)} image files to process")

        success_count = 0
        for image_file in image_files:
            try:
                if processor.process_image(str(image_file)):
                    success_count += 1
            except Exception as e:
                logger.error(f"Error processing {image_file}: {e}")

        logger.info(f"Successfully processed {success_count}/{len(image_files)} images")

        if success_count == 0:
            return 1

    else:
        logger.error(f"Input path does not exist: {input_path}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
