#!/usr/bin/env python3
"""
Test script for the Figure Diagram Processor

This script demonstrates how to use the FigureProcessor class and provides
basic testing functionality.
"""

import cv2
import numpy as np
from pathlib import Path
import logging
from figure_diagram_processor import FigureProcessor, BoundingBox

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_image_with_fig_annotation():
    """
    Create a simple test image with 'Fig 1' annotation for testing.
    
    Returns:
        numpy.ndarray: Test image
    """
    # Create a white image
    img = np.ones((400, 600, 3), dtype=np.uint8) * 255
    
    # Draw a simple figure (rectangle)
    cv2.rectangle(img, (100, 50), (500, 250), (0, 0, 0), 2)
    cv2.rectangle(img, (150, 100), (450, 200), (128, 128, 128), -1)
    
    # Add "Fig 1" text annotation
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(img, 'Fig 1', (250, 300), font, 1, (0, 0, 0), 2)
    
    return img


def create_test_image_with_dual_figs():
    """
    Create a test image with two 'Fig' annotations for testing dual processing.
    
    Returns:
        numpy.ndarray: Test image with two figures
    """
    # Create a white image
    img = np.ones((600, 800, 3), dtype=np.uint8) * 255
    
    # Draw first figure
    cv2.rectangle(img, (50, 50), (350, 200), (0, 0, 0), 2)
    cv2.rectangle(img, (75, 75), (325, 175), (100, 100, 100), -1)
    cv2.putText(img, 'Fig 1', (150, 250), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # Draw second figure
    cv2.rectangle(img, (450, 50), (750, 200), (0, 0, 0), 2)
    cv2.rectangle(img, (475, 75), (725, 175), (150, 150, 150), -1)
    cv2.putText(img, 'Fig 2', (550, 250), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    return img


def test_basic_functionality():
    """Test basic functionality of the FigureProcessor."""
    logger.info("Testing basic functionality...")
    
    # Create test directory
    test_dir = Path("test_images")
    test_dir.mkdir(exist_ok=True)
    
    # Create and save test images
    test_img1 = create_test_image_with_fig_annotation()
    test_img2 = create_test_image_with_dual_figs()
    
    cv2.imwrite(str(test_dir / "single_fig_test.png"), test_img1)
    cv2.imwrite(str(test_dir / "dual_fig_test.png"), test_img2)
    
    logger.info(f"Created test images in {test_dir}")
    
    # Initialize processor
    processor = FigureProcessor("test_output")
    
    # Test OCR detection
    logger.info("Testing OCR detection...")
    boxes1 = processor.detect_fig_boxes(test_img1)
    boxes2 = processor.detect_fig_boxes(test_img2)
    
    logger.info(f"Detected {len(boxes1)} boxes in single fig image")
    logger.info(f"Detected {len(boxes2)} boxes in dual fig image")
    
    # Test processing
    logger.info("Testing image processing...")
    
    try:
        success1 = processor.process_image(str(test_dir / "single_fig_test.png"))
        success2 = processor.process_image(str(test_dir / "dual_fig_test.png"))
        
        logger.info(f"Single fig processing: {'SUCCESS' if success1 else 'FAILED'}")
        logger.info(f"Dual fig processing: {'SUCCESS' if success2 else 'FAILED'}")
        
    except Exception as e:
        logger.error(f"Error during processing: {e}")
    
    logger.info("Basic functionality test completed")


def test_bounding_box_class():
    """Test the BoundingBox class functionality."""
    logger.info("Testing BoundingBox class...")
    
    bbox = BoundingBox(x=100, y=50, width=200, height=150, text="Fig 1", confidence=95.0)
    
    assert bbox.center_x == 200, f"Expected center_x=200, got {bbox.center_x}"
    assert bbox.center_y == 125, f"Expected center_y=125, got {bbox.center_y}"
    assert bbox.right == 300, f"Expected right=300, got {bbox.right}"
    assert bbox.bottom == 200, f"Expected bottom=200, got {bbox.bottom}"
    
    logger.info("BoundingBox class tests passed")


def test_alignment_detection():
    """Test alignment detection functionality."""
    logger.info("Testing alignment detection...")
    
    processor = FigureProcessor()
    
    # Test vertical alignment
    bbox1 = BoundingBox(100, 50, 50, 30, "Fig 1")
    bbox2 = BoundingBox(105, 150, 50, 30, "Fig 2")  # Slightly offset but within margin
    
    is_vertically_aligned = processor.are_vertically_aligned(bbox1, bbox2, 800)
    logger.info(f"Vertical alignment test: {is_vertically_aligned}")
    
    # Test horizontal alignment
    bbox3 = BoundingBox(100, 100, 50, 30, "Fig 1")
    bbox4 = BoundingBox(200, 105, 50, 30, "Fig 2")  # Slightly offset but within margin
    
    is_horizontally_aligned = processor.are_horizontally_aligned(bbox3, bbox4, 600)
    logger.info(f"Horizontal alignment test: {is_horizontally_aligned}")
    
    logger.info("Alignment detection tests completed")


def main():
    """Run all tests."""
    logger.info("Starting Figure Diagram Processor tests...")
    
    try:
        test_bounding_box_class()
        test_alignment_detection()
        test_basic_functionality()
        
        logger.info("All tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
