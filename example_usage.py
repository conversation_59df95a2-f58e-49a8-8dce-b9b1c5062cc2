#!/usr/bin/env python3
"""
Example usage of the Figure Diagram Processor

This script demonstrates various ways to use the FigureProcessor class
for processing images with 'Fig xx' annotations.
"""

import logging
from pathlib import Path
from figure_diagram_processor import FigureProcessor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def example_single_image():
    """Example: Process a single image file."""
    logger.info("Example 1: Processing a single image")
    
    # Initialize the processor with custom output directory
    processor = FigureProcessor(output_dir="example_output")
    
    # Process a single image (replace with your actual image path)
    image_path = "path/to/your/image.png"
    
    if Path(image_path).exists():
        success = processor.process_image(image_path)
        if success:
            logger.info(f"Successfully processed {image_path}")
        else:
            logger.warning(f"Failed to process {image_path}")
    else:
        logger.warning(f"Image file not found: {image_path}")


def example_batch_processing():
    """Example: Process multiple images in a directory."""
    logger.info("Example 2: Batch processing multiple images")
    
    # Initialize processor
    processor = FigureProcessor(output_dir="batch_output")
    
    # Directory containing images (replace with your actual directory)
    input_directory = Path("path/to/your/images")
    
    if input_directory.exists():
        # Find all image files
        image_extensions = ['.png', '.jpg', '.jpeg', '.tiff', '.bmp']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(input_directory.glob(f"*{ext}"))
            image_files.extend(input_directory.glob(f"*{ext.upper()}"))
        
        logger.info(f"Found {len(image_files)} image files")
        
        # Process each image
        success_count = 0
        for image_file in image_files:
            try:
                if processor.process_image(str(image_file)):
                    success_count += 1
                    logger.info(f"✓ Processed: {image_file.name}")
                else:
                    logger.warning(f"✗ Failed: {image_file.name}")
            except Exception as e:
                logger.error(f"✗ Error processing {image_file.name}: {e}")
        
        logger.info(f"Batch processing completed: {success_count}/{len(image_files)} successful")
    else:
        logger.warning(f"Input directory not found: {input_directory}")


def example_custom_configuration():
    """Example: Using custom configuration parameters."""
    logger.info("Example 3: Custom configuration")
    
    # Initialize processor with custom settings
    processor = FigureProcessor(output_dir="custom_output")
    
    # Modify configuration parameters
    processor.alignment_margin_percent = 1.0  # More lenient alignment detection
    processor.white_line_threshold = 240      # Lower threshold for white detection
    processor.black_pattern_threshold = 60    # Higher threshold for black pattern detection
    
    logger.info("Custom configuration applied:")
    logger.info(f"  Alignment margin: {processor.alignment_margin_percent}%")
    logger.info(f"  White line threshold: {processor.white_line_threshold}")
    logger.info(f"  Black pattern threshold: {processor.black_pattern_threshold}")
    
    # Process with custom settings
    image_path = "path/to/challenging/image.png"
    if Path(image_path).exists():
        success = processor.process_image(image_path)
        logger.info(f"Custom processing result: {'SUCCESS' if success else 'FAILED'}")


def example_output_analysis():
    """Example: Analyze processing results."""
    logger.info("Example 4: Analyzing processing results")
    
    output_dir = Path("example_output")
    
    if output_dir.exists():
        # Count files in each category
        categories = {
            "processed": "Successfully processed images",
            "1": "Single fig validation failed",
            "2a": "Two figs horizontal split failed", 
            "2b": "Two figs not aligned",
            "2g": "Multiple figs group split failed",
            "1g": "Single group proportional split failed",
            "3g": "Three groups proportional split failed",
            "HV": "Mixed horizontal/vertical orientations"
        }
        
        logger.info("Processing results summary:")
        total_files = 0
        
        for category, description in categories.items():
            category_dir = output_dir / category
            if category_dir.exists():
                files = list(category_dir.glob("*"))
                count = len(files)
                total_files += count
                if count > 0:
                    logger.info(f"  {category}: {count} files - {description}")
        
        logger.info(f"Total processed files: {total_files}")
    else:
        logger.warning("Output directory not found. Run processing examples first.")


def example_error_handling():
    """Example: Proper error handling."""
    logger.info("Example 5: Error handling")
    
    processor = FigureProcessor(output_dir="error_test_output")
    
    # Test with non-existent file
    try:
        result = processor.process_image("non_existent_file.png")
        logger.info(f"Non-existent file result: {result}")
    except Exception as e:
        logger.error(f"Expected error for non-existent file: {e}")
    
    # Test with invalid image format
    try:
        # Create a text file with image extension
        test_file = Path("test_invalid.png")
        test_file.write_text("This is not an image")
        
        result = processor.process_image(str(test_file))
        logger.info(f"Invalid image result: {result}")
        
        # Clean up
        test_file.unlink()
        
    except Exception as e:
        logger.error(f"Error with invalid image: {e}")


def main():
    """Run all examples."""
    logger.info("Figure Diagram Processor - Usage Examples")
    logger.info("=" * 50)
    
    # Note: These examples use placeholder paths
    # Replace with actual image paths for real testing
    
    try:
        example_single_image()
        print()
        
        example_batch_processing()
        print()
        
        example_custom_configuration()
        print()
        
        example_output_analysis()
        print()
        
        example_error_handling()
        print()
        
        logger.info("All examples completed!")
        
    except Exception as e:
        logger.error(f"Example execution failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
