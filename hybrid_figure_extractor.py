#!/usr/bin/env python3
"""
Hybrid Patent Figure Extractor

This comprehensive solution addresses the issue of missing figures by combining multiple detection methods:
1. OCR-based text label detection
2. Colored annotation box detection  
3. Edge-based contour detection
4. Connected components analysis
5. Spatial clustering of high-content regions

The hybrid approach ensures maximum figure detection coverage.
"""

import cv2
import numpy as np
import os
import argparse
from pathlib import Path
import re
from typing import List, Tuple, Dict, Optional
# from sklearn.cluster import DBSCAN  # Not needed for this implementation
import json

try:
    from paddleocr import PaddleOCR
    PADDLE_OCR_AVAILABLE = True
except ImportError:
    PADDLE_OCR_AVAILABLE = False

class HybridFigureExtractor:
    def __init__(self, output_dir: str, debug: bool = False):
        """Initialize the hybrid figure extractor."""
        self.output_dir = Path(output_dir)
        self.debug = debug
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize OCR
        self.ocr = None
        if PADDLE_OCR_AVAILABLE:
            try:
                self.ocr = PaddleOCR(use_textline_orientation=True, lang='en')
                print("PaddleOCR initialized successfully")
            except Exception as e:
                print(f"Failed to initialize PaddleOCR: {e}")
                self.ocr = None
        
        # Statistics
        self.stats = {
            'total_images': 0,
            'processed_images': 0,
            'total_figures': 0,
            'failed_images': 0,
            'method_stats': {
                'ocr_only': 0,
                'annotation_only': 0,
                'edge_only': 0,
                'hybrid': 0
            },
            'errors': []
        }
    
    def detect_ocr_labels(self, image: np.ndarray) -> List[Tuple[int, int, int, int, str, float]]:
        """Detect figure labels using OCR with lower confidence threshold."""
        if self.ocr is None:
            return []
        
        try:
            result = self.ocr.predict(image)
            
            if not result or not result[0] or 'rec_texts' not in result[0]:
                return []
            
            texts = result[0]['rec_texts']
            polys = result[0]['dt_polys']
            scores = result[0].get('rec_scores', [1.0] * len(texts))
            
            fig_labels = []
            for text, poly, score in zip(texts, polys, scores):
                # Lower confidence threshold and broader pattern matching
                if text and re.search(r'\bfig\b', text.lower()) and score > 0.3:
                    poly = np.array(poly)
                    x = int(poly[:, 0].min())
                    y = int(poly[:, 1].min())
                    w = int(poly[:, 0].max() - x)
                    h = int(poly[:, 1].max() - y)
                    fig_labels.append((x, y, w, h, text, score))
            
            return fig_labels
            
        except Exception as e:
            print(f"OCR detection failed: {e}")
            return []
    
    def detect_colored_annotations(self, image: np.ndarray) -> List[Tuple[int, int, int, int, str]]:
        """Detect colored annotation boxes with multiple color ranges."""
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Comprehensive color ranges
        color_ranges = [
            (np.array([40, 50, 50]), np.array([80, 255, 255]), "green"),
            (np.array([0, 50, 50]), np.array([10, 255, 255]), "red1"),
            (np.array([170, 50, 50]), np.array([180, 255, 255]), "red2"),
            (np.array([100, 50, 50]), np.array([130, 255, 255]), "blue"),
            (np.array([20, 50, 50]), np.array([30, 255, 255]), "yellow"),
            (np.array([140, 50, 50]), np.array([170, 255, 255]), "purple"),
        ]
        
        annotation_boxes = []
        for lower, upper, color_name in color_ranges:
            color_mask = cv2.inRange(hsv, lower, upper)
            
            # Morphological operations
            kernel = np.ones((3, 3), np.uint8)
            color_mask = cv2.morphologyEx(color_mask, cv2.MORPH_CLOSE, kernel)
            color_mask = cv2.morphologyEx(color_mask, cv2.MORPH_OPEN, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(color_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 150:  # Lower threshold
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h if h > 0 else 0
                    if 0.2 <= aspect_ratio <= 15.0:  # Very wide range
                        annotation_boxes.append((x, y, w, h, color_name))
        
        return annotation_boxes
    
    def detect_edge_contours(self, image: np.ndarray) -> List[Tuple[int, int, int, int, float, str]]:
        """Detect potential figures using edge detection with multiple approaches."""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        all_contours = []
        
        # Multiple edge detection approaches
        edge_methods = [
            (20, 80, "sensitive"),
            (30, 100, "standard"),
            (10, 50, "very_sensitive"),
            (50, 150, "conservative"),
        ]
        
        for low, high, method in edge_methods:
            edges = cv2.Canny(gray, low, high, apertureSize=3)
            
            # Different morphological operations
            kernels = [
                np.ones((3, 3), np.uint8),
                np.ones((5, 5), np.uint8),
                cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3)),
            ]
            
            for i, kernel in enumerate(kernels):
                processed_edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
                
                contours, _ = cv2.findContours(processed_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area > 1000:  # Minimum area for figures
                        x, y, w, h = cv2.boundingRect(contour)
                        aspect_ratio = w / h if h > 0 else 0
                        if 0.2 <= aspect_ratio <= 8.0 and w > 50 and h > 40:
                            all_contours.append((x, y, w, h, area, f"{method}_k{i}"))
        
        return all_contours
    
    def detect_connected_components(self, image: np.ndarray) -> List[Tuple[int, int, int, int, float]]:
        """Detect figures using connected components analysis."""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        all_components = []
        
        # Multiple thresholding approaches
        thresh_configs = [
            (0, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU),
            (100, cv2.THRESH_BINARY_INV),
            (127, cv2.THRESH_BINARY_INV),
            (150, cv2.THRESH_BINARY_INV),
            (80, cv2.THRESH_BINARY_INV),
        ]
        
        for thresh_val, thresh_type in thresh_configs:
            _, binary = cv2.threshold(gray, thresh_val, 255, thresh_type)
            
            # Connected components
            num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary, connectivity=8)
            
            for i in range(1, num_labels):  # Skip background
                area = stats[i, cv2.CC_STAT_AREA]
                if area > 800:  # Minimum area
                    x = stats[i, cv2.CC_STAT_LEFT]
                    y = stats[i, cv2.CC_STAT_TOP]
                    w = stats[i, cv2.CC_STAT_WIDTH]
                    h = stats[i, cv2.CC_STAT_HEIGHT]
                    aspect_ratio = w / h if h > 0 else 0
                    if 0.2 <= aspect_ratio <= 8.0 and w > 40 and h > 30:
                        all_components.append((x, y, w, h, area))
        
        return all_components
    
    def merge_overlapping_regions(self, regions: List[Tuple[int, int, int, int]], 
                                overlap_threshold: float = 0.5) -> List[Tuple[int, int, int, int]]:
        """Merge overlapping regions to avoid duplicates."""
        if not regions:
            return []
        
        # Sort by area (largest first)
        sorted_regions = sorted(regions, key=lambda x: x[2] * x[3], reverse=True)
        merged_regions = []
        
        for current in sorted_regions:
            x1, y1, w1, h1 = current
            
            is_duplicate = False
            for existing in merged_regions:
                x2, y2, w2, h2 = existing
                
                # Calculate overlap
                overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
                overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
                overlap_area = overlap_x * overlap_y
                
                current_area = w1 * h1
                existing_area = w2 * h2
                smaller_area = min(current_area, existing_area)
                
                if overlap_area > overlap_threshold * smaller_area:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                merged_regions.append(current)
        
        return merged_regions
    
    def combine_all_detections(self, image: np.ndarray) -> List[Tuple[int, int, int, int, str, float]]:
        """Combine all detection methods and return unified figure regions."""
        all_regions = []
        
        # 1. OCR-based detection
        ocr_labels = self.detect_ocr_labels(image)
        print(f"    OCR detected: {len(ocr_labels)} labels")
        
        # For each OCR label, try to find associated figure region
        for x, y, w, h, text, score in ocr_labels:
            # Expand search area around the label
            search_margin = 200
            search_x = max(0, x - search_margin)
            search_y = max(0, y - search_margin)
            search_w = min(image.shape[1] - search_x, w + 2 * search_margin)
            search_h = min(image.shape[0] - search_y, h + 2 * search_margin)
            
            # Look for the largest contour in this region
            search_region = image[search_y:search_y+search_h, search_x:search_x+search_w]
            gray_region = cv2.cvtColor(search_region, cv2.COLOR_BGR2GRAY)
            edges_region = cv2.Canny(gray_region, 20, 80)
            
            contours, _ = cv2.findContours(edges_region, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                largest_contour = max(contours, key=cv2.contourArea)
                area = cv2.contourArea(largest_contour)
                
                if area > 1000:
                    cx, cy, cw, ch = cv2.boundingRect(largest_contour)
                    # Convert back to full image coordinates
                    full_x = search_x + cx
                    full_y = search_y + cy
                    all_regions.append((full_x, full_y, cw, ch, f"ocr_{text}", score))
        
        # 2. Colored annotation detection
        colored_annotations = self.detect_colored_annotations(image)
        print(f"    Colored annotations: {len(colored_annotations)}")
        
        for x, y, w, h, color in colored_annotations:
            # Expand annotation box to capture associated figure
            expanded_w = max(w * 3, 150)
            expanded_h = max(h * 4, 120)
            expanded_x = max(0, x + w//2 - expanded_w//2)
            expanded_y = max(0, y - expanded_h - 10)
            
            # Ensure within image bounds
            expanded_w = min(expanded_w, image.shape[1] - expanded_x)
            expanded_h = min(expanded_h, image.shape[0] - expanded_y)
            
            if expanded_w > 50 and expanded_h > 50:
                all_regions.append((expanded_x, expanded_y, expanded_w, expanded_h, f"annotation_{color}", 0.8))
        
        # 3. Edge-based contour detection
        edge_contours = self.detect_edge_contours(image)
        print(f"    Edge contours: {len(edge_contours)}")
        
        for x, y, w, h, area, method in edge_contours:
            confidence = min(1.0, area / 10000.0)  # Area-based confidence
            all_regions.append((x, y, w, h, f"edge_{method}", confidence))
        
        # 4. Connected components
        components = self.detect_connected_components(image)
        print(f"    Connected components: {len(components)}")
        
        for x, y, w, h, area in components:
            confidence = min(1.0, area / 8000.0)  # Area-based confidence
            all_regions.append((x, y, w, h, "component", confidence))
        
        # 5. Merge overlapping regions
        basic_regions = [(x, y, w, h) for x, y, w, h, method, conf in all_regions]
        merged_regions = self.merge_overlapping_regions(basic_regions, overlap_threshold=0.4)
        
        # Convert back to full format with best method/confidence for each region
        final_regions = []
        for merged_x, merged_y, merged_w, merged_h in merged_regions:
            # Find the best detection method for this merged region
            best_method = "unknown"
            best_confidence = 0.0
            
            for x, y, w, h, method, conf in all_regions:
                # Check if this detection contributed to the merged region
                overlap_x = max(0, min(merged_x + merged_w, x + w) - max(merged_x, x))
                overlap_y = max(0, min(merged_y + merged_h, y + h) - max(merged_y, y))
                overlap_area = overlap_x * overlap_y
                
                if overlap_area > 0.3 * (w * h):  # Significant overlap
                    if conf > best_confidence:
                        best_confidence = conf
                        best_method = method
            
            final_regions.append((merged_x, merged_y, merged_w, merged_h, best_method, best_confidence))
        
        print(f"    Final merged regions: {len(final_regions)}")
        return final_regions
    
    def expand_figure_boundaries(self, image: np.ndarray,
                               bbox: Tuple[int, int, int, int],
                               expansion_factor: float = 1.2) -> Tuple[int, int, int, int]:
        """Expand figure boundaries to ensure complete capture."""
        x, y, w, h = bbox
        img_height, img_width = image.shape[:2]

        # Calculate expansion
        w_expansion = int(w * (expansion_factor - 1) / 2)
        h_expansion = int(h * (expansion_factor - 1) / 2)

        # Apply expansion with padding
        x_new = max(0, x - w_expansion - 10)
        y_new = max(0, y - h_expansion - 10)
        w_new = min(img_width - x_new, w + 2 * w_expansion + 20)
        h_new = min(img_height - y_new, h + 2 * h_expansion + 20)

        return (x_new, y_new, w_new, h_new)

    def remove_text_from_image(self, image: np.ndarray,
                             all_text_regions: List[Tuple[int, int, int, int, str, float]]) -> np.ndarray:
        """Remove 'Fig xx' text from the extracted figure image using DNN text detection."""
        cleaned_image = image.copy()

        # Try to use DNN text detection models (more accurate than OCR)
        try:
            # Path to the DNN models
            db18_model_path = "Image text detect/DB_TD500_resnet18.onnx"
            db50_model_path = "Image text detect/DB_TD500_resnet50.onnx"

            # Check if model files exist
            if os.path.exists(db18_model_path) and os.path.exists(db50_model_path):
                # Initialize text detectors
                textDetectorDB18 = cv2.dnn_TextDetectionModel_DB(db18_model_path)
                textDetectorDB50 = cv2.dnn_TextDetectionModel_DB(db50_model_path)

                # Configure detection parameters
                bin_thresh = 0.3
                poly_thresh = 0.5
                mean = (122.67891434, 116.66876762, 104.00698793)

                # Resize image for detection (models work best with specific sizes)
                h, w = cleaned_image.shape[:2]
                detection_size = (320, 320)
                resized_image = cv2.resize(cleaned_image, detection_size)

                # Configure both detectors
                textDetectorDB18.setBinaryThreshold(bin_thresh).setPolygonThreshold(poly_thresh)
                textDetectorDB18.setInputParams(1.0/255, detection_size, mean, True)
                textDetectorDB50.setBinaryThreshold(bin_thresh).setPolygonThreshold(poly_thresh)
                textDetectorDB50.setInputParams(1.0/255, detection_size, mean, True)

                # Detect text regions
                boxesDB18, _ = textDetectorDB18.detect(resized_image)
                boxesDB50, _ = textDetectorDB50.detect(resized_image)

                # Combine all detected boxes
                all_boxes = []
                if boxesDB18 is not None:
                    all_boxes.extend(boxesDB18)
                if boxesDB50 is not None:
                    all_boxes.extend(boxesDB50)

                if all_boxes:
                    # Create mask for text removal on original size image
                    inpaint_mask = np.zeros(cleaned_image.shape[:2], dtype=np.uint8)

                    for box in all_boxes:
                        # Scale box coordinates back to original image size
                        scaled_box = []
                        for point in box:
                            scaled_x = int(point[0] * w / detection_size[0])
                            scaled_y = int(point[1] * h / detection_size[1])
                            scaled_box.append([scaled_x, scaled_y])

                        # Create mask on original size image
                        cv2.fillPoly(inpaint_mask, [np.array(scaled_box, np.int32)], 255)

                    # Expand mask slightly to ensure complete text removal
                    kernel = np.ones((5, 5), np.uint8)
                    inpaint_mask = cv2.dilate(inpaint_mask, kernel, iterations=2)

                    # Apply inpainting to remove text
                    cleaned_image = cv2.inpaint(cleaned_image, inpaint_mask, inpaintRadius=5, flags=cv2.INPAINT_NS)

                    print(f"      Removed {len(all_boxes)} text regions using DNN detection")
                else:
                    print(f"      No text regions detected by DNN models")

            else:
                print(f"      DNN models not found, falling back to OCR text removal")
                # Fall back to OCR-based text removal
                cleaned_image = self.remove_text_with_ocr(cleaned_image)

        except Exception as e:
            print(f"      DNN text detection failed: {e}, falling back to OCR")
            # Fall back to OCR-based text removal
            cleaned_image = self.remove_text_with_ocr(cleaned_image)

        return cleaned_image

    def remove_text_with_ocr(self, image: np.ndarray) -> np.ndarray:
        """Fallback text removal using OCR."""
        cleaned_image = image.copy()

        if self.ocr is not None:
            try:
                result = self.ocr.predict(cleaned_image)

                if result and result[0] and 'rec_texts' in result[0]:
                    texts = result[0]['rec_texts']
                    polys = result[0]['dt_polys']
                    scores = result[0].get('rec_scores', [1.0] * len(texts))

                    for text, poly, score in zip(texts, polys, scores):
                        # Remove any text that looks like figure labels
                        if text and re.search(r'\bfig\b', text.lower()) and score > 0.2:
                            poly = np.array(poly)

                            # Create a mask for the text region
                            mask = np.zeros(cleaned_image.shape[:2], dtype=np.uint8)
                            cv2.fillPoly(mask, [poly.astype(np.int32)], 255)

                            # Expand the mask
                            kernel = np.ones((7, 7), np.uint8)
                            mask = cv2.dilate(mask, kernel, iterations=3)

                            # Use inpainting to remove the text
                            cleaned_image = cv2.inpaint(cleaned_image, mask, 5, cv2.INPAINT_TELEA)

                            print(f"      Removed text: '{text}' from extracted figure")

                        # Also remove other common text patterns
                        elif text and (re.search(r'^\d+[a-z]?$', text.lower()) or
                                     re.search(r'^[a-z]\d*$', text.lower()) or
                                     re.search(r'^\d+$', text.lower()) or
                                     re.search(r'^[a-z]$', text.lower()) or
                                     len(text.strip()) <= 3) and score > 0.4:
                            poly = np.array(poly)

                            mask = np.zeros(cleaned_image.shape[:2], dtype=np.uint8)
                            cv2.fillPoly(mask, [poly.astype(np.int32)], 255)

                            kernel = np.ones((3, 3), np.uint8)
                            mask = cv2.dilate(mask, kernel, iterations=1)

                            cleaned_image = cv2.inpaint(cleaned_image, mask, 3, cv2.INPAINT_TELEA)

                            print(f"      Removed label text: '{text}' from extracted figure")

            except Exception as e:
                print(f"      Warning: OCR text removal failed: {e}")

        return cleaned_image

    def process_image(self, image_path: str) -> Dict:
        """Process a single image using hybrid detection."""
        result = {
            'image_path': image_path,
            'success': False,
            'figures_extracted': 0,
            'detection_methods': [],
            'output_files': [],
            'error': None
        }
        
        try:
            print(f"Processing: {os.path.basename(image_path)}")
            
            # Read image
            image = cv2.imread(image_path)
            if image is None:
                result['error'] = f"Could not read image: {image_path}"
                return result
            
            print(f"  Image shape: {image.shape}")
            
            # Combine all detection methods
            all_figures = self.combine_all_detections(image)

            if not all_figures:
                result['error'] = "No figures detected by any method"
                return result

            # Get all text regions for text removal
            all_text_regions = self.detect_ocr_labels(image)

            # Create output directory
            image_name = Path(image_path).stem
            image_output_dir = self.output_dir / image_name
            image_output_dir.mkdir(exist_ok=True)
            
            # Extract each figure
            for i, (x, y, w, h, method, confidence) in enumerate(all_figures):
                print(f"  Extracting figure {i+1}: method={method}, confidence={confidence:.3f}")
                
                # Expand boundaries
                expanded_bbox = self.expand_figure_boundaries(image, (x, y, w, h), expansion_factor=1.2)
                
                # Extract figure
                ex, ey, ew, eh = expanded_bbox
                if ew > 0 and eh > 0 and ex >= 0 and ey >= 0 and ex + ew <= image.shape[1] and ey + eh <= image.shape[0]:
                    figure_image = image[ey:ey+eh, ex:ex+ew].copy()

                    # Remove 'Fig xx' text from the extracted figure
                    print(f"    Removing text from figure {i+1}...")
                    cleaned_figure = self.remove_text_from_image(figure_image, all_text_regions)

                    # Save cleaned figure
                    figure_filename = f"{image_name}_figure_{i+1:02d}.png"
                    figure_path = image_output_dir / figure_filename

                    success = cv2.imwrite(str(figure_path), cleaned_figure)
                    if success:
                        result['output_files'].append(str(figure_path))
                        result['figures_extracted'] += 1
                        result['detection_methods'].append(method)
                        print(f"    ✓ Saved: {figure_filename}")
                    else:
                        print(f"    ✗ Failed to save: {figure_filename}")
            
            # Create debug visualization
            if self.debug:
                debug_image = image.copy()
                
                for i, (x, y, w, h, method, conf) in enumerate(all_figures):
                    color = (0, 255, 0) if "ocr" in method else (255, 0, 0) if "edge" in method else (0, 0, 255)
                    cv2.rectangle(debug_image, (x, y), (x + w, y + h), color, 2)
                    cv2.putText(debug_image, f"{i+1}:{method[:4]}", (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
                
                debug_path = image_output_dir / f"{image_name}_debug.png"
                cv2.imwrite(str(debug_path), debug_image)
                print(f"    Saved debug: {debug_path.name}")
            
            result['success'] = True
            print(f"  ✓ Successfully extracted {result['figures_extracted']} figures using hybrid approach")
            
        except Exception as e:
            result['error'] = str(e)
            print(f"  ✗ Error: {e}")
        
        return result
    
    def process_all_images(self, input_dir: str) -> None:
        """Process all images in the input directory."""
        input_path = Path(input_dir)
        image_files = list(input_path.glob("*.png"))
        self.stats['total_images'] = len(image_files)
        
        if not image_files:
            print(f"No PNG files found in {input_dir}")
            return
        
        print(f"Found {len(image_files)} images to process")
        print(f"Output directory: {self.output_dir}")
        
        for i, image_path in enumerate(image_files, 1):
            print(f"\n[{i}/{len(image_files)}]")
            
            result = self.process_image(str(image_path))
            
            if result['success']:
                self.stats['processed_images'] += 1
                self.stats['total_figures'] += result['figures_extracted']
                
                # Track method usage
                for method in result['detection_methods']:
                    if "ocr" in method:
                        self.stats['method_stats']['ocr_only'] += 1
                    elif "annotation" in method:
                        self.stats['method_stats']['annotation_only'] += 1
                    elif "edge" in method:
                        self.stats['method_stats']['edge_only'] += 1
                    else:
                        self.stats['method_stats']['hybrid'] += 1
            else:
                self.stats['failed_images'] += 1
                self.stats['errors'].append(result['error'])
        
        self.print_summary()
    
    def print_summary(self) -> None:
        """Print processing summary."""
        print(f"\n{'='*60}")
        print("HYBRID FIGURE EXTRACTION SUMMARY")
        print(f"{'='*60}")
        print(f"Total images found: {self.stats['total_images']}")
        print(f"Successfully processed: {self.stats['processed_images']}")
        print(f"Failed to process: {self.stats['failed_images']}")
        print(f"Total figures extracted: {self.stats['total_figures']}")
        
        if self.stats['processed_images'] > 0:
            avg_figures = self.stats['total_figures'] / self.stats['processed_images']
            print(f"Average figures per image: {avg_figures:.1f}")
        
        print(f"\nDetection method usage:")
        for method, count in self.stats['method_stats'].items():
            print(f"  {method}: {count}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Hybrid patent figure extractor with maximum coverage")
    parser.add_argument("input_dir", nargs="?", default="annotated_design_output",
                       help="Input directory containing annotated images")
    parser.add_argument("output_dir", nargs="?", default="hybrid_design_figures",
                       help="Output directory for extracted figures")
    parser.add_argument("--debug", action="store_true",
                       help="Enable debug mode (saves visualization images)")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_dir):
        print(f"Error: Input directory '{args.input_dir}' does not exist")
        return 1
    
    extractor = HybridFigureExtractor(args.output_dir, debug=args.debug)
    extractor.process_all_images(args.input_dir)
    
    return 0

if __name__ == "__main__":
    exit(main())
